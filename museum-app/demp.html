<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人博物馆App原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7fafc;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding: 20px;
        }
        .app-container {
            width: 375px; /* Standard mobile width */
            height: 667px; /* Standard mobile height */
            background-color: #ffffff;
            border-radius: 2rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            position: relative;
            padding-bottom: 60px; /* Space for the tab bar */
            overflow: hidden; /* Hide scrollbar and manage pages inside */
        }
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #ffffff;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 60px;
            z-index: 100;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.75rem;
            line-height: 1rem;
            color: #a0aec0;
            transition: color 0.2s;
        }
        .tab-item.active {
            color: #4c51bf;
        }
        .tab-item svg {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
        }
        .page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            overflow-y: auto;
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
            transform: translateX(100%);
            opacity: 0;
            z-index: 10;
            padding-bottom: 60px; /* Space for the tab bar on most pages */
        }
        .page.active {
            transform: translateX(0);
            opacity: 1;
        }
        #item-detail-page, #my-gallery-page {
            padding-bottom: 0; /* Full height for detail page */
            display: flex;
            flex-direction: column;
        }
        .content {
            padding: 1rem;
        }
        .card {
            border-radius: 1rem;
            background-color: #f0f4f8;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .pill {
            display: inline-block;
            background-color: #e2e8f0;
            color: #4a5568;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
        }
        .pill.active {
            background-color: #4c51bf;
            color: #ffffff;
        }
        .gallery-thumbnail {
            width: 64px;
            height: 64px;
            object-fit: cover;
            border-radius: 0.5rem;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s;
        }
        .gallery-thumbnail.active {
            border-color: #4c51bf;
        }
        .item-detail-header, .item-detail-image-section {
            flex-shrink: 0; /* Prevents these sections from shrinking */
        }
        .item-detail-description-section {
            flex-grow: 1; /* Allows this section to fill the remaining space */
            overflow-y: auto; /* Makes this section scrollable */
            padding: 1rem;
        }
        .carousel-container {
            position: relative;
            width: 100%;
            height: 256px; /* 64 * 4 */
            overflow: hidden;
            border-radius: 0.5rem;
            background-color: #ffffff;
            border: 8px solid #f0f4f8;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 1rem;
        }
        .carousel-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: opacity 0.5s ease-in-out; 
        }
        .carousel-image.active {
            opacity: 1;
        }
        .play-pause-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .carousel-images-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }
        /* Style for the new category modal */
        #category-modal {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 200;
            display: none;
            flex-direction: column;
            justify-content: flex-end;
            transition: opacity 0.3s ease-in-out;
        }
        #category-modal-content {
            background-color: #ffffff;
            border-radius: 1rem 1rem 0 0;
            padding: 1.5rem;
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
            max-height: 80%;
            overflow-y: auto;
        }
        #category-modal.active {
            display: flex;
            opacity: 1;
        }
        #category-modal.active #category-modal-content {
            transform: translateY(0);
        }
        .category-list-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center">

<div class="app-container" id="app-container">
    <!-- Community/Home Page -->
    <div id="community-page" class="page active">
        <div class="p-4 flex items-center justify-between">
            <h1 class="text-2xl font-bold text-gray-800" id="page-title">热门收藏</h1>
            <button class="w-10 h-10 bg-indigo-600 text-white rounded-full text-2xl font-bold flex items-center justify-center shadow-lg" onclick="showPage('add-item-page')">
                +
            </button>
        </div>
        <div class="px-4 py-2">
            <!-- New category selection button -->
            <div class="flex items-center space-x-4 mb-4">
                <span class="text-sm text-gray-500">分类:</span>
                <button id="community-category-select-btn" class="pill flex items-center justify-between w-40">
                    <span id="community-selected-category-text">全部</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>
            <div id="community-sort-options" class="flex items-center space-x-4">
                <span class="text-sm text-gray-500">排序:</span>
                <button class="pill active" data-sort="likes">热度</button>
                <button class="pill" data-sort="time">时间</button>
            </div>
        </div>

        <div class="content grid grid-cols-2 gap-4" id="community-items-container">
            <!-- Items will be rendered here dynamically -->
        </div>
    </div>

    <!-- Add Item Page (Hidden by default) -->
    <div id="add-item-page" class="page">
        <div class="p-4 flex items-center justify-start">
            <h1 class="text-2xl font-bold text-gray-800">添加物品</h1>
        </div>
        <div class="content">
            <div class="bg-white p-6 rounded-xl shadow-lg">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">拍照或上传照片</label>
                    <div class="mt-2 w-full h-40 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500">
                        点击上传图片
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">物品名称</label>
                    <input type="text" placeholder="例如：iPhone 6s Plus" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm p-2">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">所属分类</label>
                    <select class="mt-1 block w-full rounded-md border-gray-300 shadow-sm p-2">
                        <option>手机</option>
                        <option>汽车</option>
                        <option>电子产品</option>
                        <option>收藏品</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">时间（年份）</label>
                    <input type="number" placeholder="例如：2015" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm p-2">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">备注 / 故事</label>
                    <textarea rows="4" placeholder="写下物品背后的故事..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm p-2"></textarea>
                </div>
                <button class="w-full bg-indigo-600 text-white font-bold py-3 px-4 rounded-full shadow-lg">保存</button>
            </div>
        </div>
    </div>
    
    <!-- Profile Page (Hidden by default) -->
    <div id="profile-page" class="page">
        <div class="p-4 flex items-center justify-between">
            <h1 class="text-2xl font-bold text-gray-800">个人中心</h1>
        </div>
        <div class="content flex flex-col items-center">
            <img src="https://placehold.co/100x100/d1d5db/4b5563?text=头像" alt="用户头像" class="w-24 h-24 rounded-full border-4 border-white shadow-lg">
            <h2 class="text-xl font-bold mt-4 text-gray-800">数字旅人</h2>
            <p class="text-gray-500 text-sm mt-1">我的展馆：已记录 4 件物品</p>
            <button class="mt-4 px-6 py-2 bg-indigo-600 text-white rounded-full shadow-lg">订阅状态：基础版</button>
            <div class="mt-8 w-full">
                <!-- My Gallery button with onclick handler -->
                <div class="bg-white rounded-xl shadow-lg p-4 flex items-center mb-4 cursor-pointer" onclick="showPage('my-gallery-page')">
                    <svg class="w-6 h-6 text-indigo-600 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
                    我的展馆
                </div>
                <div class="bg-white rounded-xl shadow-lg p-4 flex items-center mb-4">
                    <svg class="w-6 h-6 text-indigo-600 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v2a3 3 0 11-6 0v-2m6 0H9"></path></svg>
                    我的评论 / 点赞
                </div>
                <div class="bg-white rounded-xl shadow-lg p-4 flex items-center">
                    <svg class="w-6 h-6 text-indigo-600 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37-2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                    设置
                </div>
            </div>
        </div>
    </div>

    <!-- My Gallery Page (New Page) -->
    <div id="my-gallery-page" class="page">
        <div class="p-4 flex items-center justify-start">
            <button onclick="showPage('profile-page')" class="text-indigo-600 font-bold text-xl">
                &lt; 返回
            </button>
            <h1 class="text-2xl font-bold text-gray-800 ml-4">我的展馆</h1>
        </div>
        <!-- My Gallery's own sorting and filtering options -->
        <div class="px-4 py-2">
            <div class="flex items-center space-x-4 mb-4">
                <span class="text-sm text-gray-500">分类:</span>
                <button id="my-gallery-category-select-btn" class="pill flex items-center justify-between w-40">
                    <span id="my-gallery-selected-category-text">全部</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>
            <div id="my-gallery-sort-options" class="flex items-center space-x-4">
                <span class="text-sm text-gray-500">排序:</span>
                <button class="pill active" data-sort="likes">热度</button>
                <button class="pill" data-sort="time">时间</button>
            </div>
        </div>
        <div class="content grid grid-cols-2 gap-4" id="my-gallery-items-container">
            <!-- Items belonging to '数字旅人' will be rendered here -->
        </div>
    </div>

    <!-- Item Detail Page (Hidden by default) -->
    <div id="item-detail-page" class="page">
        <!-- Fixed Header -->
        <div class="item-detail-header p-4 flex items-center justify-between border-b border-gray-200 bg-white">
            <!-- Back button with dynamic onclick handler -->
            <button id="detail-back-btn" class="text-indigo-600 font-bold text-xl">
                &lt; 返回
            </button>
            <h1 class="text-xl font-bold text-gray-800" id="detail-item-name">藏品名称</h1>
            <div class="w-10"></div>
        </div>
        
        <!-- Fixed Image Section -->
        <div class="item-detail-image-section p-4 bg-white">
            <div class="carousel-container relative rounded-lg">
                <div class="carousel-images-wrapper" id="carousel-images">
                    <!-- Images will be injected here -->
                </div>
                <!-- Play/Pause Button -->
                <button id="play-pause-btn" class="play-pause-btn">
                    <!-- SVG icons for play and pause -->
                    <svg id="play-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.752 11.18c-.461-.284-.969-.426-1.523-.426s-1.062.142-1.523.426L6.2 7.749A.96.96 0 005 8.529v7.942a.96.96 0 001.2.78l6.029-3.447c.461-.284.969-.426 1.523-.426s1.062.142 1.523.426l6.029 3.447a.96.96 0 001.2-.78V8.529a.96.96 0 00-1.2-.78l-6.029 3.447z"/>
                    </svg>
                    <svg id="pause-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10 19a2 2 0 01-2-2V7a2 2 0 012-2h4a2 2 0 012 2v10a2 2 0 01-2 2h-4z"/>
                    </svg>
                </button>
            </div>
            <div id="image-gallery" class="flex overflow-x-auto space-x-2 p-2">
                <!-- Thumbnails will be rendered here -->
            </div>
        </div>
        
        <!-- Scrollable Description Section -->
        <div class="item-detail-description-section">
            <div class="bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-xl font-bold text-gray-800" id="detail-item-title"></h2>
                <p class="text-gray-500 text-sm mt-1" id="detail-item-info"></p>
                <p class="text-gray-500 text-sm mt-1">👍 <span id="detail-item-likes"></span></p>
                <p class="mt-4 text-gray-700 leading-relaxed" id="detail-item-story"></p>
            </div>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <a href="#" class="tab-item active" data-page="community-page">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>
            <span>首页</span>
        </a>
        <a href="#" class="tab-item" data-page="add-item-page">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span>添加</span>
        </a>
        <a href="#" class="tab-item" data-page="profile-page">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>
            <span>我的</span>
        </a>
    </div>

    <!-- Category Modal -->
    <div id="category-modal">
        <div id="category-modal-content" class="bg-white p-6 rounded-t-2xl">
            <div class="category-list-header">
                <button id="category-back-btn" onclick="renderCategoryList(true)" class="text-indigo-600 font-bold hidden">
                    &lt; 返回
                </button>
                <h3 id="category-modal-title" class="text-xl font-bold flex-grow text-center">选择分类</h3>
                <button onclick="hideCategoryModal()" class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div id="category-list" class="grid grid-cols-2 gap-4">
                <!-- Categories will be injected here -->
            </div>
        </div>
    </div>

</div>

<script>
    // 模拟的嵌套分类数据
    const nestedCategories = {
        '数码产品': ['手机', '电脑', '相机', '平板', '智能家居'],
        '生活用品': ['家具', '厨具', '服装', '鞋子'],
        '收藏品': ['邮票', '硬币', '艺术品', '老物件'],
        '旅行纪念品': ['明信片', '当地特产', '照片'],
        '测试': ['测试项目A', '测试项目B']
    };

    const mockCommunityItems = [
        { 
            id: 1, 
            name: 'iPhone 4', 
            category: '手机', 
            parentCategory: '数码产品',
            year: 2010, 
            likes: 150, 
            user: '张三', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=iPhone+4',
            story: '这款经典的iPhone 4是苹果公司的一款重要产品，它采用了全新的设计语言，双面玻璃和不锈钢中框使其极具辨识度。这是我高中时期的梦想，虽然晚了几年才入手，但依旧承载了满满的回忆。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=iPhone+4+正面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=iPhone+4+背面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=iPhone+4+侧面'
            ]
        },
        { 
            id: 2, 
            name: '诺基亚 N95', 
            category: '手机', 
            parentCategory: '数码产品',
            year: 2007, 
            likes: 120, 
            user: '李四', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=诺基亚+N95',
            story: '诺基亚N95是那个年代的机皇，双向滑盖的设计非常独特。它拥有强大的多媒体功能和内置GPS，在智能机尚未普及的时代，它就是移动娱乐的代表。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=诺基亚+N95+正面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=诺基亚+N95+滑盖',
                'https://placehold.co/600x800/d1d5db/4b5563?text=诺基亚+N95+键盘'
            ]
        },
        { 
            id: 3, 
            name: '大众 Beetle', 
            category: '汽车', 
            parentCategory: '交通工具', // New category
            year: 1999, 
            likes: 210, 
            user: '王五', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=大众+Beetle',
            story: '这辆经典的大众甲壳虫是我的第一辆车，它小巧可爱，陪伴我度过了很多美好的时光。虽然现在已经换了车，但我仍然会时常怀念它。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=大众+Beetle+正面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=大众+Beetle+侧面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=大众+Beetle+内饰'
            ]
        },
        { 
            id: 4, 
            name: '老式相机', 
            category: '相机', 
            parentCategory: '数码产品',
            year: 1980, 
            likes: 95, 
            user: '小明', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=老式相机',
            story: '一台在跳蚤市场淘到的老式相机，虽然功能已经过时，但机械的质感和复古的造型让人爱不释手。我喜欢把它摆在书桌上，静静地欣赏。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=老式相机+正面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=老式相机+特写'
            ]
        },
        { 
            id: 5, 
            name: 'Kindle Paperwhite', 
            category: '平板', 
            parentCategory: '数码产品',
            year: 2018, 
            likes: 88, 
            user: '数字旅人', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=Kindle+Paperwhite',
            story: '我的Kindle陪伴我度过了许多次长途旅行，它是我在飞机、火车上的最佳伴侣。E-ink屏幕让我能够长时间舒适地阅读，它是我数字世界中的一方净土。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=Kindle+正面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=Kindle+阅读',
            ]
        },
        { 
            id: 6, 
            name: '毕业纪念品', 
            category: '明信片', 
            parentCategory: '旅行纪念品',
            year: 2017, 
            likes: 75, 
            user: '小红', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=毕业纪念品',
            story: '这是我大学毕业时收到的纪念品，虽然只是一个小小的摆件，但它承载着我四年青春的记忆。每次看到它，都会想起那段美好的校园时光。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=毕业纪念品+特写',
                'https://placehold.co/600x800/d1d5db/4b5563?text=毕业纪念品+背面',
            ]
        },
        { 
            id: 7, 
            name: '索尼 Walkman', 
            category: '播放器', 
            parentCategory: '数码产品',
            year: 1995, 
            likes: 180, 
            user: '老王', 
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=索尼+Walkman',
            story: '这台索尼Walkman是我年轻时的最爱，它让我第一次体验到了随时随地听音乐的快乐。虽然磁带已经成为历史，但它独特的音质和情怀是无法替代的。',
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=Walkman+正面',
                'https://placehold.co/600x800/d1d5db/4b5563?text=Walkman+打开',
                'https://placehold.co/600x800/d1d5db/4b5563?text=Walkman+特写'
            ]
        },
        {
            id: 8,
            name: '加长版测试项目',
            category: '测试项目A',
            parentCategory: '测试',
            year: 2025,
            likes: 999,
            user: '数字旅人',
            imageUrl: 'https://placehold.co/400x500/d1d5db/4b5563?text=长文本测试',
            story: `这是一个非常非常长的故事，用来测试页面滚动功能。这个故事没有实际意义，只是为了填充内容。
            这个故事讲述了一个名叫小明的年轻人，他决定踏上一段寻找自我价值的旅程。他辞掉了在大城市里的高薪工作，卖掉了自己的公寓，只带上了一台老旧的相机和一本泛黄的日记本，便出发了。
            他的第一站是中国的云南。那里的山川壮丽，云雾缭绕，每一片梯田都像一面镜子，映照着天空的颜色。他用相机记录下了这些美丽的瞬间，也结识了许多热情好客的当地人。他们给他讲述了许多关于这片土地的传说，每一个故事都充满了神秘和智慧。
            接下来，他去了冰岛。那里的黑沙滩、冰川和北极光让他感受到了大自然的震撼力量。他在一个名叫“冰川湖”的地方，看到了漂浮在湖面上的巨大冰块，它们在阳光下闪烁着蓝色的光芒。他感觉自己仿佛置身于另一个星球，所有的烦恼都变得微不足道。
            他的旅程还在继续，他去了南美的雨林，非洲的草原，也去了南极的冰原。在旅途中，他遇到了各种各样的人，有背包客，有探险家，也有像他一样，正在寻找自己人生答案的旅人。他们相互分享着彼此的故事，也成为了彼此旅途中的重要伙伴。
            最终，小明回到了自己的家乡。虽然他的旅程结束了，但他的人生才刚刚开始。他不再是那个只知道工作赚钱的年轻人，他变得更加成熟，也更加懂得感恩。他开始用自己的相机和日记，将他的旅程故事分享给更多的人，鼓励他们也去探索自己的内心世界。
            这个故事虽然很长，但它告诉我们，人生的意义不在于终点，而在于沿途的风景和遇到的每一个人。每一次的出发，都是为了更好地回家。`,
            images: [
                'https://placehold.co/600x800/d1d5db/4b5563?text=长文本测试+图1',
                'https://placehold.co/600x800/d1d5db/4b5563?text=长文本测试+图2'
            ]
        }
    ];

    let communityCurrentCategory = '全部';
    let communityCurrentParentCategory = '';
    let communityCurrentSort = 'likes';
    let myGalleryCurrentCategory = '全部';
    let myGalleryCurrentParentCategory = '';
    let myGalleryCurrentSort = 'likes';
    let previousPage = 'community-page';
    let carouselInterval = null;
    let isPlaying = true;
    let currentImageIndex = 0;
    let carouselImages = [];

    // 新增一个变量来存储详情页的返回地址
    let detailPreviousPageId = '';

    // Refactored a generic render function to be used by multiple pages
    function renderItems(containerId, filterUser, category, parentCategory, sort, fromPageId) {
        let itemsToDisplay = [...mockCommunityItems];

        // Filter by user if specified
        if (filterUser) {
            itemsToDisplay = itemsToDisplay.filter(item => item.user === filterUser);
        }
        
        // Filter by category
        if (category !== '全部') {
            if (parentCategory) {
                itemsToDisplay = itemsToDisplay.filter(item => 
                    item.parentCategory === parentCategory && item.category === category
                );
            } else {
                itemsToDisplay = itemsToDisplay.filter(item => item.category === category || item.parentCategory === category);
            }
        }
        
        // Sort items
        if (sort === 'likes') {
            itemsToDisplay.sort((a, b) => b.likes - a.likes);
        } else if (sort === 'time') {
            itemsToDisplay.sort((a, b) => b.year - a.year);
        }

        const container = document.getElementById(containerId);
        container.innerHTML = ''; // Clear existing content

        itemsToDisplay.forEach(item => {
            const itemHtml = `
                <div class="bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" onclick="showPage('item-detail-page', ${item.id}, '${fromPageId}')">
                    <img src="${item.imageUrl}" alt="${item.name}" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-semibold text-sm">${item.name}</h3>
                        <p class="text-gray-500 text-xs mt-1">
                            ${item.year}年 · ${item.user} · 👍 ${item.likes}
                        </p>
                    </div>
                </div>
            `;
            container.innerHTML += itemHtml;
        });
    }

    function renderItemDetailPage(item, fromPageId) {
        document.getElementById('detail-item-name').innerText = item.name;
        document.getElementById('detail-item-title').innerText = item.name;
        document.getElementById('detail-item-info').innerText = `${item.category} · ${item.year}年 · by ${item.user}`;
        document.getElementById('detail-item-likes').innerText = item.likes;
        document.getElementById('detail-item-story').innerText = item.story;
        
        // Render carousel images
        const carouselContainer = document.getElementById('carousel-images');
        carouselContainer.innerHTML = '';
        carouselImages = item.images.map((url, index) => {
            const img = document.createElement('img');
            img.src = url;
            img.alt = `图片 ${index + 1}`;
            img.classList.add('carousel-image');
            if (index === 0) {
                img.classList.add('active');
            }
            carouselContainer.appendChild(img);
            return img;
        });

        currentImageIndex = 0;
        
        // Render thumbnail gallery
        const galleryContainer = document.getElementById('image-gallery');
        galleryContainer.innerHTML = '';
        item.images.forEach((url, index) => {
            const img = document.createElement('img');
            img.src = url;
            img.alt = `图片 ${index + 1}`;
            img.classList.add('gallery-thumbnail');
            if (index === 0) {
                img.classList.add('active');
            }
            img.onclick = () => {
                currentImageIndex = index;
                updateCarousel();
                if (isPlaying) {
                    clearInterval(carouselInterval);
                    togglePlayPause();
                }
            };
            galleryContainer.appendChild(img);
        });

        // Set up play/pause functionality
        document.getElementById('play-pause-btn').onclick = togglePlayPause;
        
        // Start auto-play
        if (carouselInterval) clearInterval(carouselInterval);
        isPlaying = false; // Set to false initially so togglePlayPause starts it
        togglePlayPause(); 
    }

    function updateCarousel() {
        // Hide all images
        carouselImages.forEach(img => img.classList.remove('active'));
        // Show the active image
        carouselImages[currentImageIndex].classList.add('active');

        // Update thumbnail active state
        document.querySelectorAll('.gallery-thumbnail').forEach((t, index) => {
            if (index === currentImageIndex) {
                t.classList.add('active');
            } else {
                t.classList.remove('active');
            }
        });
    }

    function togglePlayPause() {
        const playIcon = document.getElementById('play-icon');
        const pauseIcon = document.getElementById('pause-icon');

        if (isPlaying) {
            clearInterval(carouselInterval);
            isPlaying = false;
            playIcon.classList.remove('hidden');
            pauseIcon.classList.add('hidden');
        } else {
            carouselInterval = setInterval(() => {
                currentImageIndex = (currentImageIndex + 1) % carouselImages.length;
                updateCarousel();
            }, 3000);
            isPlaying = true;
            playIcon.classList.add('hidden');
            pauseIcon.classList.remove('hidden');
        }
    }

    // 更新 showPage 函数，使其支持从特定页面返回
    function showPage(pageId, itemId = null, fromPageId = null) {
        const activePage = document.querySelector('.page.active');
        if (activePage) {
            // 只有在页面切换时才更新 previousPage
            if (pageId !== activePage.id) {
                previousPage = activePage.id;
            }
            activePage.classList.remove('active');
        }

        const newPage = document.getElementById(pageId);
        newPage.classList.add('active');

        const tabBar = document.querySelector('.tab-bar');

        if (pageId === 'item-detail-page') {
            tabBar.style.display = 'none';
            // 修复：在这里记录从哪个页面进入了详情页
            detailPreviousPageId = fromPageId;
            if (itemId) {
                const item = mockCommunityItems.find(i => i.id === itemId);
                if (item) {
                    renderItemDetailPage(item, fromPageId);
                }
            }
        } else {
            tabBar.style.display = 'flex';
        }

        document.querySelectorAll('.tab-item').forEach(item => item.classList.remove('active'));
        const activeTab = document.querySelector(`.tab-item[data-page="${pageId}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }
        
        if (pageId !== 'item-detail-page' && carouselInterval) {
            clearInterval(carouselInterval);
            carouselInterval = null;
        }

        if (pageId === 'community-page') {
            renderItems('community-items-container', null, communityCurrentCategory, communityCurrentParentCategory, communityCurrentSort, 'community-page');
        } else if (pageId === 'my-gallery-page') {
            renderItems('my-gallery-items-container', '数字旅人', myGalleryCurrentCategory, myGalleryCurrentParentCategory, myGalleryCurrentSort, 'my-gallery-page');
        }
    }
    
    // Functions for the category modal
    let activePageForModal = '';

    function showCategoryModal(pageId) {
        activePageForModal = pageId;
        document.getElementById('category-modal').classList.add('active');
        renderCategoryList();
    }

    function hideCategoryModal() {
        document.getElementById('category-modal').classList.remove('active');
    }

    function renderCategoryList(isBack = false) {
        const listContainer = document.getElementById('category-list');
        const backBtn = document.getElementById('category-back-btn');
        const modalTitle = document.getElementById('category-modal-title');
        
        if (isBack) {
            if (activePageForModal === 'community-page') communityCurrentParentCategory = '';
            if (activePageForModal === 'my-gallery-page') myGalleryCurrentParentCategory = '';
        }
        
        listContainer.innerHTML = '';
        backBtn.classList.add('hidden');
        modalTitle.innerText = '选择分类';

        // Render top-level categories and '全部'
        const topCategories = ['全部', ...Object.keys(nestedCategories)];

        topCategories.forEach(category => {
            const categoryHtml = `
                <div class="p-3 text-center bg-gray-100 rounded-lg cursor-pointer hover:bg-indigo-100 transition" 
                     onclick="handleCategoryClick('${category}')">
                     ${category}
                </div>
            `;
            listContainer.innerHTML += categoryHtml;
        });
    }

    function renderSubCategories(parentCategory) {
        const listContainer = document.getElementById('category-list');
        const backBtn = document.getElementById('category-back-btn');
        const modalTitle = document.getElementById('category-modal-title');

        listContainer.innerHTML = '';
        backBtn.classList.remove('hidden');
        modalTitle.innerText = parentCategory;

        const subCategories = nestedCategories[parentCategory];
        subCategories.forEach(category => {
            const categoryHtml = `
                <div class="p-3 text-center bg-gray-100 rounded-lg cursor-pointer hover:bg-indigo-100 transition" 
                     onclick="selectCategory('${category}', '${parentCategory}')">
                     ${category}
                </div>
            `;
            listContainer.innerHTML += categoryHtml;
        });
    }

    function handleCategoryClick(category) {
        if (category === '全部') {
            selectCategory('全部', '');
        } else if (nestedCategories[category]) {
            if (activePageForModal === 'community-page') communityCurrentParentCategory = category;
            if (activePageForModal === 'my-gallery-page') myGalleryCurrentParentCategory = category;
            renderSubCategories(category);
        } else {
            selectCategory(category, '');
        }
    }

    function selectCategory(category, parentCategory) {
        let displayText = category;
        if (parentCategory) {
            displayText = `${parentCategory} > ${category}`;
        }
        
        if (activePageForModal === 'community-page') {
            communityCurrentCategory = category;
            communityCurrentParentCategory = parentCategory;
            document.getElementById('community-selected-category-text').innerText = displayText;
            renderItems('community-items-container', null, communityCurrentCategory, communityCurrentParentCategory, communityCurrentSort, 'community-page');
        } else if (activePageForModal === 'my-gallery-page') {
            myGalleryCurrentCategory = category;
            myGalleryCurrentParentCategory = parentCategory;
            document.getElementById('my-gallery-selected-category-text').innerText = displayText;
            renderItems('my-gallery-items-container', '数字旅人', myGalleryCurrentCategory, myGalleryCurrentParentCategory, myGalleryCurrentSort, 'my-gallery-page');
        }
        
        hideCategoryModal();
    }

    // 修复：详情页返回按钮点击事件
    document.getElementById('detail-back-btn').addEventListener('click', () => {
        if (detailPreviousPageId) {
            showPage(detailPreviousPageId);
        } else {
            // 默认返回主页
            showPage('community-page');
        }
    });
    
    // Community Page event listeners
    document.querySelectorAll('#community-sort-options button').forEach(pill => {
        pill.addEventListener('click', (e) => {
            document.querySelectorAll('#community-sort-options button').forEach(p => p.classList.remove('active'));
            e.target.classList.add('active');
            communityCurrentSort = e.target.dataset.sort;
            renderItems('community-items-container', null, communityCurrentCategory, communityCurrentParentCategory, communityCurrentSort, 'community-page');
        });
    });

    document.getElementById('community-category-select-btn').addEventListener('click', () => showCategoryModal('community-page'));

    // My Gallery Page event listeners
    document.querySelectorAll('#my-gallery-sort-options button').forEach(pill => {
        pill.addEventListener('click', (e) => {
            document.querySelectorAll('#my-gallery-sort-options button').forEach(p => p.classList.remove('active'));
            e.target.classList.add('active');
            myGalleryCurrentSort = e.target.dataset.sort;
            renderItems('my-gallery-items-container', '数字旅人', myGalleryCurrentCategory, myGalleryCurrentParentCategory, myGalleryCurrentSort, 'my-gallery-page');
        });
    });

    document.getElementById('my-gallery-category-select-btn').addEventListener('click', () => showCategoryModal('my-gallery-page'));

    document.getElementById('category-back-btn').addEventListener('click', () => renderCategoryList(true));

    document.querySelectorAll('.tab-bar a').forEach(tab => {
        tab.addEventListener('click', (e) => {
            e.preventDefault();
            const pageId = e.currentTarget.dataset.page;
            showPage(pageId);
        });
    });

    // 初始页面加载
    document.addEventListener('DOMContentLoaded', () => {
        const initialPage = document.getElementById('community-page');
        initialPage.classList.add('active');
        initialPage.style.transform = 'translateX(0)';
        initialPage.style.opacity = '1';
        renderItems('community-items-container', null, communityCurrentCategory, communityCurrentParentCategory, communityCurrentSort, 'community-page');
    });
</script>

</body>
</html>
